package net.summerfarm.wnc.application.service.changeTask;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.context.FenceChangeContext;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.application.service.changeTask.factory.FenceChangeTaskContextFactory;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsQueryDomainService;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义区域切订单服务
 * date: 2025/9/5 16:21<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForCustomFenceOrderChangeHandleService {

    // 错误信息常量
    private static final String ERROR_OLD_CUSTOM_FENCE_NOT_MATCHED = "未匹配到旧的自定义围栏";
    private static final String ERROR_OLD_FENCE_NOT_MATCHED = "未匹配到旧的围栏";
    private static final String ERROR_OLD_FENCE_NOT_FOUND = "未匹配查询到旧的围栏";
    private static final String ERROR_NOT_IN_VALID_DELIVERY_AREA = "不在有效配送区域";

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private FenceChangeTaskContextFactory fenceChangeTaskContextFactory;
    @Resource
    private WncFenceChangeRecordsQueryDomainService wncFenceChangeRecordsQueryDomainService;
    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    @Resource
    private FenceRepository fenceRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private FenceChangeTaskSender fenceChangeTaskSender;

    /**
     * 自定义区域切订单处理
     */
    public void customFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1. 构建围栏变更上下文
        FenceChangeContext context = buildFenceChangeContext(waitOrderChangeHandleTask);
        if (context == null) {
            return;
        }

        // 2. 判断围栏变更类型并获取变更前的仓库编号
        FenceChangeTypeEnum changeType = wncFenceChangeRecordsQueryDomainService.determineCustomFenceChangeType(context.getBeforeFenceChangeRecords());
        List<Integer> beforeStoreNos = getBeforeStoreNos(context, changeType, waitOrderChangeHandleTask);
        if (beforeStoreNos == null) {
            return; // 已在方法内部处理完结任务
        }

        // 3. 查询并保存订单数据
        List<FenceChangeTaskOrderEntity> orderEntities = queryAndSaveOrders(context, beforeStoreNos, waitOrderChangeHandleTask);
        if (CollectionUtils.isEmpty(orderEntities)) {
            completeTask(waitOrderChangeHandleTask);
            return;
        }

        // 4. 处理订单围栏匹配
        processOrderFenceMatching(orderEntities, context, changeType);

        // 5. 执行订单切仓处理
        executeOrderChangeHandling(orderEntities, waitOrderChangeHandleTask);
    }

    /**
     * 构建围栏变更上下文
     */
    private FenceChangeContext buildFenceChangeContext(FenceChangeTaskEntity task) {
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities =
            wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(task.getId());

        if (CollectionUtils.isEmpty(fenceChangeRecordsEntities)) {
            log.info("围栏变更记录为空，跳过处理，任务ID:{}", task.getId());
            return null;
        }

        String changeBatchNo = fenceChangeRecordsEntities.get(0).getChangeBatchNo();
        List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaChangeWarehouseRecordsEntities =
            wncCityAreaChangeWarehouseRecordsQueryRepository.selectByFenceChangeTaskIds(Collections.singletonList(task.getId()));

        FenceChangeContext context = fenceChangeTaskContextFactory.buildFenceChangeContext(changeBatchNo, cityAreaChangeWarehouseRecordsEntities);
        if (context == null) {
            log.info("自定义围栏切仓订单任务ID:{} 无围栏变更上下文，直接完结任务", task.getId());
        }
        return context;
    }

    /**
     * 获取变更前的仓库编号列表
     */
    private List<Integer> getBeforeStoreNos(FenceChangeContext context, FenceChangeTypeEnum changeType, FenceChangeTaskEntity task) {
        List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords = context.getBeforeFenceChangeRecords();
        String city = context.getCity();
        String area = context.getArea();

        switch (changeType) {
            case CUSTOM_TO_CUSTOM:
                return beforeFenceChangeRecords.stream()
                    .map(WncFenceChangeRecordsEntity::getFenceStoreNo)
                    .distinct()
                    .collect(Collectors.toList());

            case NONE_TO_CUSTOM:
                // 不涉及切仓直接完结掉即可
                completeTask(task);
                return null;

            case NORMAL_TO_CUSTOM:
                return getStoreNosForNormalToCustom(context, beforeFenceChangeRecords, city, area);

            default:
                log.error("自定义围栏切仓订单处理任务，围栏类型异常，changeBatchNo: {}",
                    context.getBeforeFenceChangeRecords().get(0).getChangeBatchNo());
                return Collections.emptyList();
        }
    }

    /**
     * 处理普通围栏到自定义围栏的仓库编号获取
     */
    private List<Integer> getStoreNosForNormalToCustom(FenceChangeContext context,
                                                       List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords,
                                                       String city, String area) {
        List<WncFenceAreaChangeRecordsEntity> fenceAreaChangeRecords = context.beforeAreaChangeRecordsGet();

        List<WncFenceAreaChangeRecordsEntity> matchedAreaChangeRecords = fenceAreaChangeRecords.stream()
                .filter(e -> Objects.equals(e.getCity(), city) && Objects.equals(e.getArea(), area))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(matchedAreaChangeRecords)) {
            return Collections.emptyList();
        }

        Integer fenceId = matchedAreaChangeRecords.get(0).getFenceId();
        return beforeFenceChangeRecords.stream()
                .filter(e -> Objects.equals(e.getFenceId(), fenceId))
                .map(WncFenceChangeRecordsEntity::getFenceStoreNo)
                .collect(Collectors.toList());
    }

    /**
     * 查询并保存订单数据
     */
    private List<FenceChangeTaskOrderEntity> queryAndSaveOrders(FenceChangeContext context,
                                                                List<Integer> beforeStoreNos,
                                                                FenceChangeTaskEntity task) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();
        String city = context.getCity();
        String area = context.getArea();

        for (Integer storeNo : beforeStoreNos) {
            FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                    .city(city)
                    .areas(Collections.singletonList(area))
                    .storeNo(storeNo)
                    .deliveryDateBegin(task.getExeTimePlus2Date()) // T+2的时间点
                    .build();
            List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
            allFulfillmentOrders.addAll(fulfillmentOrders);
        }

        if (CollectionUtils.isEmpty(allFulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FenceChangeTaskOrderEntity> orderEntities = allFulfillmentOrders.stream()
                .map(FenceChangeTaskOrderConverter::dto2Entity)
                .collect(Collectors.toList());

        fenceChangeTaskDomainService.saveBatchDetail(orderEntities);
        return orderEntities;
    }

    /**
     * 处理订单围栏匹配
     */
    private void processOrderFenceMatching(List<FenceChangeTaskOrderEntity> orderEntities,
                                          FenceChangeContext context,
                                          FenceChangeTypeEnum changeType) {
        List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList = context.beforeAreaChangeRecordsGet();
        List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsList = context.afterAreaChangeRecordsGet();

        orderEntities.forEach(orderInfo -> {
            if (FenceChangeTypeEnum.CUSTOM_TO_CUSTOM.equals(changeType)) {
                processCustomToCustomMatching(orderInfo, beforeAreaChangeRecordsList, afterAreaChangeRecordsList);
            } else {
                processNormalToCustomMatching(orderInfo, beforeAreaChangeRecordsList, afterAreaChangeRecordsList);
            }
        });
    }

    /**
     * 处理自定义围栏到自定义围栏的匹配
     */
    private void processCustomToCustomMatching(FenceChangeTaskOrderEntity orderInfo,
                                              List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList,
                                              List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsList) {
        String orderCity = orderInfo.getCity();
        String orderArea = orderInfo.getArea();
        String orderPoi = orderInfo.getPoi();

        // 匹配旧围栏区域
        Integer beforeAdCodeMsgId = matchFenceByPoi(beforeAreaChangeRecordsList, orderCity, orderArea, orderPoi, false);
        if (beforeAdCodeMsgId == null) {
            fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), ERROR_OLD_CUSTOM_FENCE_NOT_MATCHED);
            return;
        }

        // 匹配新围栏区域（有效配送区域）
        Integer afterAdCodeMsgId = matchFenceByPoi(afterAreaChangeRecordsList, orderCity, orderArea, orderPoi, true);
        if (afterAdCodeMsgId == null) {
            fenceChangeTaskDomainService.orderChangeNoNeedHandle(orderInfo.getId(), ERROR_NOT_IN_VALID_DELIVERY_AREA);
            return;
        }

        // 设置围栏信息
        setFenceInfo(orderInfo, beforeAdCodeMsgId, afterAdCodeMsgId);
    }

    /**
     * 处理普通围栏到自定义围栏的匹配
     */
    private void processNormalToCustomMatching(FenceChangeTaskOrderEntity orderInfo,
                                              List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList,
                                              List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsList) {
        String orderCity = orderInfo.getCity();
        String orderArea = orderInfo.getArea();
        String orderPoi = orderInfo.getPoi();

        // 匹配订单所属的旧围栏区域
        WncFenceAreaChangeRecordsEntity beforeMatchedAreaChangeRecord = beforeAreaChangeRecordsList.stream()
                .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                .findFirst()
                .orElse(null);

        if (beforeMatchedAreaChangeRecord == null) {
            fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), ERROR_OLD_FENCE_NOT_MATCHED);
            return;
        }

        Integer beforeAdCodeMsgId = beforeMatchedAreaChangeRecord.getAdCodeMsgId();
        Integer beforeFenceId = beforeMatchedAreaChangeRecord.getFenceId();

        // 验证旧围栏信息
        FenceEntity beforeFenceEntity = fenceRepository.queryById(beforeFenceId);
        if (beforeFenceEntity == null) {
            fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), ERROR_OLD_FENCE_NOT_FOUND);
            return;
        }

        // 匹配新围栏区域（有效配送区域）
        Integer afterAdCodeMsgId = matchFenceByPoi(afterAreaChangeRecordsList, orderCity, orderArea, orderPoi, true);
        if (afterAdCodeMsgId == null) {
            fenceChangeTaskDomainService.orderChangeNoNeedHandle(orderInfo.getId(), ERROR_NOT_IN_VALID_DELIVERY_AREA);
            return;
        }

        // 设置围栏信息
        setFenceInfoForNormalToCustom(orderInfo, beforeAdCodeMsgId, beforeFenceEntity, afterAdCodeMsgId);
    }



    /**
     * 通过POI匹配围栏
     */
    private Integer matchFenceByPoi(List<WncFenceAreaChangeRecordsEntity> areaChangeRecords,
                                   String orderCity, String orderArea, String orderPoi,
                                   boolean onlyValidDeliveryArea) {
        List<Integer> adCodeMsgIds = areaChangeRecords.stream()
                .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                .filter(e -> !onlyValidDeliveryArea ||
                    Objects.equals(e.getAdCodeMsgDetailEntity().getStatus(), AdCodeMsgEnums.Status.VALID.getValue()))
                .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(adCodeMsgIds)) {
            return null;
        }

        return customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(adCodeMsgIds, orderPoi);
    }

    /**
     * 设置围栏信息（自定义到自定义）
     */
    private void setFenceInfo(FenceChangeTaskOrderEntity orderInfo, Integer beforeAdCodeMsgId, Integer afterAdCodeMsgId) {
        // 注意：这里原代码有bug，查询旧围栏信息时使用了afterAdCodeMsgId，应该使用beforeAdCodeMsgId
        Map<Integer, FenceEntity> beforeAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(beforeAdCodeMsgId));
        FenceEntity beforeFenceEntity = beforeAdMsgIdTOFenceEntityMap.get(beforeAdCodeMsgId);

        Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
        FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

        if (beforeFenceEntity == null || afterFenceEntity == null) {
            fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "围栏信息查询失败");
            return;
        }

        orderInfo.setOldAdCodeMsgId(beforeAdCodeMsgId);
        orderInfo.setOldFenceId(beforeFenceEntity.getId());
        orderInfo.setOldStoreNo(beforeFenceEntity.getStoreNo());
        orderInfo.setOldAreaNo(beforeFenceEntity.getAreaNo());

        orderInfo.setNewAdCodeMsgId(afterAdCodeMsgId);
        orderInfo.setNewFenceId(afterFenceEntity.getId());
        orderInfo.setNewStoreNo(afterFenceEntity.getStoreNo());
        orderInfo.setNewAreaNo(afterFenceEntity.getAreaNo());

        if (Objects.equals(orderInfo.getOldStoreNo(), orderInfo.getNewStoreNo()) && Objects.equals(orderInfo.getOldAreaNo(), orderInfo.getNewAreaNo())
                && Objects.equals(orderInfo.getOldFenceId(), orderInfo.getNewFenceId()) && Objects.equals(orderInfo.getOldAdCodeMsgId(), orderInfo.getNewAdCodeMsgId())) {
            fenceChangeTaskDomainService.orderChangeNoNeedHandle(orderInfo.getId(), "无需处理");
            return;
        } else {
            fenceChangeTaskDomainService.updateOldNewFenceInfo(orderInfo);
        }

    }

    /**
     * 设置围栏信息（普通到自定义）
     */
    private void setFenceInfoForNormalToCustom(FenceChangeTaskOrderEntity orderInfo,
                                              Integer beforeAdCodeMsgId,
                                              FenceEntity beforeFenceEntity,
                                              Integer afterAdCodeMsgId) {
        Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
        FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

        if (afterFenceEntity == null) {
            fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "新围栏信息查询失败");
            return;
        }

        orderInfo.setOldAdCodeMsgId(beforeAdCodeMsgId);
        orderInfo.setOldFenceId(beforeFenceEntity.getId());
        orderInfo.setOldStoreNo(beforeFenceEntity.getStoreNo());
        orderInfo.setOldAreaNo(beforeFenceEntity.getAreaNo());

        orderInfo.setNewAdCodeMsgId(afterAdCodeMsgId);
        orderInfo.setNewFenceId(afterFenceEntity.getId());
        orderInfo.setNewStoreNo(afterFenceEntity.getStoreNo());
        orderInfo.setNewAreaNo(afterFenceEntity.getAreaNo());

        fenceChangeTaskDomainService.updateOldNewFenceInfo(orderInfo);
    }

    /**
     * 执行订单切仓处理
     */
    private void executeOrderChangeHandling(List<FenceChangeTaskOrderEntity> orderEntities, FenceChangeTaskEntity task) {
        List<String> failOrders = new ArrayList<>();

        orderEntities.forEach(orderInfo -> {
            try {
                fenceChangeTaskDomainService.doFenceChangeOrderHandleNew(orderInfo);
            } catch (Exception e) {
                failOrders.add(orderInfo.getOuterOrderId());
                log.info("围栏切仓订单处理任务-履约单处理失败，等待失败订单重试，异常原因：{}", e.getMessage(), e);
                FenceChangeTaskOrderEntity updateOrderFail = orderInfo.execute(FenceChangeTaskDetailEnums.Status.FAIL, e.getMessage());
                fenceChangeTaskDetailRepository.update(updateOrderFail);
            }
        });

        // 更新切仓任务状态
        completeTask(task);

        // 发送失败通知
        if (!failOrders.isEmpty()) {
            fenceChangeTaskSender.sendOrderFailMsg(task, failOrders.size());
        }
    }

    /**
     * 完成任务
     */
    private void completeTask(FenceChangeTaskEntity task) {
        fenceChangeTaskRepository.update(task.execute(FenceChangeTaskEnums.Status.COMPLETED));
    }
}
