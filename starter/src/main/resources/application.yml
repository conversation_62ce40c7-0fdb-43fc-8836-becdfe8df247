server:
  port: 80
  servlet:
    context-path: /summerfarm-wnc

spring:
  application:
    name: summerfarm-wnc
  profiles:
    active: dev2

logging:
  level:
    # Easy-ES 核心包日志
    com.xpc.easyes.core.conditions: DEBUG
    # ES 请求日志（显示DSL语句）
    org.elasticsearch.client: DEBUG
    # 事务相关日志
    com.xpc.easyes.core.toolkit: DEBUG
    # 其他相关包
    com.xpc.easyes: DEBUG

mybatis-plus:
  mapper-locations: classpath:net/summerfarm/wnc/infrastructure/mapper/*.xml
  global-config:
    db-config:
      update-strategy: NOT_NULL
      field-strategy: not_empty
      id-type: auto
      db-type: mysql
  configuration:
    # sql 打印
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql