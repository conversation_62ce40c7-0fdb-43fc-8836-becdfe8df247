package net.summerfarm.wnc.repository;

import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CustomFenceAreaEsQueryRepositoryTest {

    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    @Resource
    private CustomFenceAreaEsCommandRepository customFenceAreaEsCommandRepository;

    @Test
    public void matchEsByAdCodeMsgIdsWithPoi() {
        Integer i = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(Arrays.asList(1), "120.130396,30.259242");
        System.out.println(i);
    }

    @Test
    public void test(){
        String a = "120.272911,30.321279;120.27259,30.320521;120.272633,30.32045;120.273528,30.32004;120.275559,30.319851;120.277843,30.319577;120.285424,30.318669;120.285597,30.318647;120.291956,30.317819;120.291626,30.315325;120.29346,30.31509;120.293469,30.314543;120.293256,30.313633;120.29347,30.312876;120.293788,30.31252;120.294351,30.309899;120.294544,30.309666;120.294657,30.309241;120.294723,30.308296;120.294887,30.307906;120.295119,30.307254;120.295187,30.306732;120.2955,30.306291;120.295205,30.300986;120.295093,30.300019;120.294775,30.297627;120.294664,30.297031;120.294548,30.296966;120.29441,30.296288;120.293942,30.292046;120.293782,30.289563;120.288131,30.288234;120.284431,30.287585;120.27896,30.286827;120.27059,30.284724;120.269792,30.284102;120.266138,30.282484;120.26015,30.279677;120.252846,30.276087;120.248041,30.272472;120.241658,30.265134;120.221777,30.23859;120.221058,30.237666;120.218373,30.235087;120.213306,30.230547;120.211297,30.228716;120.208613,30.226515;120.203348,30.222039;120.196561,30.216566;120.187584,30.211087;120.182867,30.207929;120.177622,30.205626;120.165029,30.201311;120.148695,30.195716;120.142721,30.19364;120.140885,30.193007;120.140175,30.192838;120.138174,30.195124;120.137577,30.196607;120.137109,30.198094;120.137162,30.199817;120.137552,30.201017;120.138729,30.201879;120.139011,30.202017;120.138974,30.20225;120.139089,30.202635;120.13901,30.202925;120.139301,30.203607;120.139121,30.203884;120.138726,30.204109;120.138715,30.204304;120.138789,30.204436;120.139577,30.204933;120.140486,30.205265;120.141761,30.205465;120.141867,30.205481;120.143443,30.205618;120.143295,30.206497;120.14283,30.206474;120.141518,30.208281;120.140903,30.209819;120.140904,30.210194;120.140904,30.210327;120.140721,30.210531;120.140678,30.210841;120.140263,30.211671;120.139874,30.212841;120.139599,30.213392;120.139838,30.213886;120.139974,30.213989;120.140996,30.214096;120.141586,30.213731;120.141682,30.213114;120.142021,30.212839;120.143229,30.212545;120.144126,30.212444;120.147551,30.212529;120.148152,30.21298;120.148959,30.214155;120.149224,30.214367;120.14995,30.214704;120.150626,30.215656;120.151173,30.216264;120.152571,30.216874;120.152684,30.216976;120.152723,30.217277;120.152914,30.217503;120.15353,30.217704;120.153602,30.217727;120.154017,30.21765;120.153943,30.216916;120.154194,30.216734;120.154461,30.216398;120.154581,30.215874;120.154655,30.215757;120.154763,30.215738;120.154967,30.216178;120.155124,30.216841;120.156004,30.217709;120.156589,30.218442;120.156763,30.21915;120.157209,30.219461;120.157504,30.219806;120.158059,30.220601;120.158449,30.221372;120.158926,30.221932;120.159326,30.222628;120.159799,30.223739;120.159801,30.224876;120.159443,30.225431;120.159579,30.226391;120.160349,30.227487;120.160488,30.227901;120.160488,30.228281;120.160107,30.228787;120.15989,30.229178;120.159717,30.22966;120.159467,30.231104;120.159397,30.231343;120.159233,30.231584;120.157372,30.232861;120.155338,30.233767;120.154386,30.234414;120.154334,30.234539;120.1541,30.235159;120.154048,30.235534;120.154083,30.235974;120.154161,30.23622;120.154213,30.236265;120.154282,30.236287;120.154369,30.236291;120.154498,30.236267;120.154567,30.236249;120.154602,30.236245;120.154619,30.236273;120.154611,30.23631;120.154602,30.236347;120.154585,30.236384;120.154576,30.236419;120.154576,30.236451;120.154602,30.236473;120.154646,30.236481;120.154671,30.236525;120.154585,30.236549;120.154507,30.236571;120.154438,30.236582;120.154394,30.236586;120.154342,30.236606;120.154291,30.236649;120.15423,30.236723;120.154204,30.236786;120.154221,30.236819;120.154247,30.236847;120.15423,30.236923;120.154204,30.236979;120.154213,30.23701;120.154213,30.237055;120.154169,30.237064;120.154135,30.237192;120.154213,30.237216;120.154152,30.237261;120.154117,30.23729;120.154135,30.237357;120.154187,30.237372;120.154152,30.237422;120.154152,30.237468;120.154169,30.237489;120.154204,30.237504;120.154239,30.237526;120.154273,30.237585;120.154308,30.237617;120.154342,30.23765;120.154351,30.237686;120.154377,30.237795;120.154369,30.237821;120.154377,30.237886;120.154395,30.23791;120.154395,30.237942;120.154395,30.237997;120.154421,30.238025;120.154429,30.238077;120.154446,30.23822;120.154455,30.238463;120.154412,30.238644;120.154369,30.238654;120.154351,30.238676;120.15436,30.238789;120.154343,30.238839;120.154299,30.238843;120.154247,30.238946;120.154247,30.238978;120.154196,30.239067;120.15417,30.239176;120.15417,30.239221;120.154196,30.239236;120.154196,30.239282;120.154187,30.239419;120.15417,30.239493;120.15417,30.239542;120.154178,30.239569;120.154204,30.239595;120.154222,30.239644;120.15423,30.239692;120.154222,30.239738;120.154247,30.23977;120.154247,30.239822;120.154265,30.239909;120.154291,30.240026;120.154334,30.240206;120.154395,30.24043;120.154438,30.240581;120.15449,30.240612;120.15449,30.240658;120.154507,30.240742;120.154507,30.24079;120.154542,30.240838;120.154551,30.240874;120.154585,30.240985;120.154577,30.241016;120.154611,30.241061;120.154654,30.241115;120.154724,30.241143;120.154767,30.241167;120.154802,30.241217;120.154836,30.241269;120.154897,30.241308;120.154923,30.241358;120.155001,30.24144;120.155053,30.241533;120.155087,30.241544;120.155122,30.241581;120.1552,30.24167;120.155243,30.241722;120.155243,30.241752;120.155287,30.241832;120.155338,30.241869;120.155373,30.241886;120.155399,30.241949;120.155434,30.241967;120.15546,30.241958;120.155477,30.241988;120.155468,30.242032;120.155503,30.242058;120.155607,30.242142;120.15578,30.242263;120.155798,30.242303;120.155832,30.242372;120.155875,30.242383;120.155901,30.242396;120.155988,30.242456;120.155988,30.242513;120.156014,30.242541;120.156057,30.242547;120.156117,30.24258;120.156169,30.242642;120.156221,30.242723;120.156256,30.242723;120.156291,30.24274;120.156334,30.242807;120.156369,30.242857;120.156429,30.242863;120.156447,30.242946;120.156464,30.24297;120.156524,30.242981;120.156551,30.242996;120.156611,30.243065;120.156689,30.243212;120.156767,30.243195;120.15681,30.243238;120.157104,30.2435;120.157485,30.243854;120.157866,30.244207;120.158178,30.244428;120.158213,30.244471;120.158359,30.244594;120.15855,30.244809;120.158628,30.244947;120.158819,30.245193;120.158879,30.245257;120.158974,30.245329;120.159061,30.2454;120.159147,30.24547;120.159199,30.245532;120.159346,30.245675;120.159424,30.245741;120.159519,30.245836;120.159649,30.245966;120.159719,30.246035;120.159762,30.246087;120.159779,30.246115;120.159762,30.246146;120.159745,30.246213;120.159745,30.246256;120.159779,30.246282;120.159823,30.246291;120.159866,30.246297;120.159892,30.246317;120.159918,30.246341;120.159944,30.246369;120.160004,30.246384;120.160178,30.246403;120.160316,30.246407;120.160428,30.246407;120.160506,30.246405;120.160506,30.246463;120.16048,30.246524;120.160472,30.246624;120.160437,30.246837;120.16042,30.246874;120.160359,30.246889;120.160273,30.246887;120.160221,30.246857;120.160212,30.246826;120.160212,30.246709;120.160203,30.246605;120.16016,30.246564;120.160108,30.246562;120.160065,30.246571;120.160022,30.246586;120.159961,30.246582;120.159909,30.246571;120.159849,30.24656;120.159753,30.24653;120.159693,30.246471;120.159641,30.24645;120.159572,30.246415;120.159511,30.246391;120.159442,30.246357;120.15939,30.246342;120.159364,30.24632;120.159312,30.246259;120.15926,30.246222;120.159182,30.246208;120.159104,30.246203;120.159026,30.246197;120.158948,30.246192;120.158905,30.246171;120.158853,30.246158;120.158819,30.246156;120.158749,30.246167;120.158654,30.246187;120.158593,30.246191;120.158516,30.246202;120.158464,30.246209;120.158386,30.246202;120.158308,30.246194;120.158247,30.246207;120.158135,30.246231;120.158066,30.246268;120.158048,30.246311;120.158039,30.246389;120.158022,30.246433;120.157979,30.24644;120.157979,30.246468;120.15797,30.246542;120.157962,30.24662;120.15797,30.246674;120.157979,30.24672;120.157996,30.246752;120.158014,30.246782;120.158031,30.246839;120.158066,30.246891;120.158091,30.246902;120.158118,30.246915;120.158143,30.24698;120.158161,30.247023;120.158161,30.247067;120.158169,30.247097;120.158195,30.247118;120.158239,30.24716;120.158264,30.247246;120.158308,30.247303;120.158343,30.247339;120.15836,30.247374;120.158394,30.247439;120.158421,30.247453;120.158455,30.247485;120.15849,30.247567;120.158507,30.247591;120.158533,30.247626;120.158602,30.247721;120.158637,30.247754;120.158663,30.247766;120.158698,30.247782;120.158732,30.247803;120.158784,30.247845;120.158819,30.247857;120.158879,30.247836;120.158914,30.247831;120.158957,30.247842;120.158992,30.247837;120.159009,30.247809;120.159035,30.247792;120.159087,30.247772;120.159148,30.247764;120.159225,30.247757;120.159294,30.247753;120.159338,30.2478;120.159373,30.247852;120.159468,30.247847;120.15952,30.247843;120.159632,30.24783;120.159719,30.247841;120.159831,30.247843;120.160204,30.247786;120.160299,30.247775;120.160333,30.24777;120.160342,30.247733;120.160411,30.247724;120.160437,30.247757;120.16042,30.247796;120.160454,30.247881;120.160481,30.247941;120.160498,30.248;120.160515,30.248074;120.160524,30.248134;120.160532,30.248176;120.160532,30.248215;120.160481,30.248219;120.160437,30.248148;120.160411,30.247959;120.160385,30.247896;120.160368,30.247846;120.160316,30.247827;120.1601,30.247849;120.159866,30.247878;120.15965,30.247891;120.15958,30.247902;120.159494,30.247926;120.159442,30.247947;120.159398,30.247967;120.159373,30.247998;120.159364,30.248028;120.159347,30.248058;120.159286,30.248078;120.159234,30.2481;120.159165,30.248126;120.159113,30.248133;120.159061,30.248148;120.159026,30.248178;120.159026,30.248235;120.159044,30.248285;120.159061,30.248328;120.159096,30.248393;120.15913,30.248493;120.159148,30.248545;120.159174,30.248597;120.159208,30.248664;120.15926,30.248744;120.159329,30.248814;120.159408,30.248868;120.159451,30.248905;120.159485,30.24897;120.15952,30.249048;120.159572,30.249167;120.159615,30.249245;120.159676,30.24931;120.159719,30.249388;120.159806,30.249456;120.159858,30.249496;120.159927,30.249531;120.159987,30.249546;120.160031,30.249529;120.160074,30.249524;120.160117,30.249541;120.160186,30.249524;120.160273,30.249448;120.160334,30.249463;120.160385,30.249456;120.160429,30.249411;120.160533,30.249302;120.160558,30.249259;120.160602,30.249198;120.160619,30.249172;120.160602,30.249141;120.160576,30.249089;120.160611,30.249067;120.160662,30.249085;120.160706,30.249074;120.160749,30.249072;120.160766,30.249117;120.160775,30.249161;120.160792,30.249263;120.160766,30.249289;120.160619,30.249386;120.160584,30.249371;120.160524,30.249374;120.160489,30.249402;120.160429,30.249452;120.16042,30.24948;120.160403,30.24953;120.160316,30.249621;120.16029,30.249687;120.160308,30.249723;120.160325,30.249752;120.160368,30.249782;120.160394,30.249795;120.160411,30.249838;120.160429,30.249869;120.160463,30.249884;120.160507,30.249888;120.160576,30.249879;120.160602,30.249896;120.160784,30.249994;120.160801,30.250044;120.160827,30.250057;120.160861,30.250074;120.160888,30.250089;120.160939,30.250102;120.160974,30.250104;120.161017,30.250102;120.161052,30.250119;120.161069,30.250156;120.161104,30.25018;120.161147,30.250199;120.161191,30.250219;120.161234,30.250241;120.161286,30.250258;120.161329,30.25026;120.161372,30.250258;120.161424,30.250193;120.161407,30.250264;120.161407,30.250294;120.161416,30.250325;120.161433,30.250362;120.16145,30.250396;120.161476,30.250438;120.161511,30.250483;120.161545,30.250511;120.161571,30.250529;120.161606,30.250557;120.161641,30.250583;120.161675,30.25062;120.161718,30.25065;120.161753,30.250663;120.161822,30.25068;120.1619,30.250689;120.16203,30.250697;120.162056,30.250717;120.161978,30.250775;120.161944,30.250817;120.1619,30.250879;120.161883,30.250916;120.161883,30.250949;120.161961,30.251023;120.162039,30.251081;120.162073,30.251114;120.162099,30.251133;120.162151,30.251157;120.162194,30.251148;120.16235,30.251044;120.162385,30.251013;120.162411,30.250992;120.162454,30.251019;120.162471,30.251048;120.162498,30.251089;120.162515,30.251133;120.162515,30.251178;120.162515,30.251226;120.162498,30.251414;120.162498,30.251529;120.162498,30.251562;120.162489,30.251606;120.162463,30.251667;120.162333,30.251897;120.162022,30.252414;120.161944,30.252533;120.161918,30.252572;120.1619,30.252614;120.161822,30.252789;120.161779,30.25285;120.161753,30.252883;120.161701,30.252918;120.161658,30.252992;120.161476,30.25324;120.161251,30.253544;120.161052,30.253766;120.161009,30.253766;120.160966,30.25377;120.160931,30.253789;120.160896,30.253814;120.160871,30.253848;120.160862,30.253879;120.160853,30.253935;120.160879,30.253994;120.160706,30.254257;120.160438,30.254637;120.160204,30.254959;120.160031,30.255202;120.159641,30.255997;120.159624,30.256045;120.15959,30.256084;120.159564,30.256121;120.159512,30.256169;120.15946,30.256191;120.159391,30.256223;120.159339,30.256269;120.159278,30.256354;120.159235,30.25639;120.159157,30.256404;120.159071,30.256421;120.158975,30.256458;120.158871,30.256465;120.158785,30.256467;120.158637,30.256467;120.158594,30.256466;120.158352,30.256461;120.158196,30.256629;120.157374,30.257559;120.156941,30.258045;120.157304,30.258275;120.157639,30.258398;120.158694,30.258262;120.159522,30.258248;120.161468,30.258318;120.161511,30.258314;120.161632,30.258249;120.161673,30.258211;120.161712,30.258205;120.161737,30.258215;120.161746,30.258253;120.161832,30.258318;120.161866,30.258326;120.163511,30.258364;120.16358,30.258349;120.163814,30.25824;120.163866,30.258236;120.163909,30.258257;120.16403,30.25835;120.164073,30.258366;120.165752,30.258301;120.168132,30.258171;120.168357,30.258158;120.169405,30.258114;120.169734,30.258094;120.171283,30.258024;120.171309,30.258013;120.171347,30.257972;120.171431,30.257893;120.171544,30.257892;120.171642,30.257895;120.171672,30.2579;120.171694,30.257906;120.171802,30.258006;120.176121,30.257937;120.176173,30.257917;120.176276,30.257833;120.176319,30.257813;120.176372,30.257815;120.176415,30.257832;120.176553,30.257921;120.176605,30.257932;120.180552,30.257994;120.180586,30.257985;120.180673,30.257879;120.180699,30.257859;120.180751,30.257852;120.180803,30.257861;120.180846,30.257885;120.180915,30.257963;120.180941,30.257984;120.180976,30.257995;120.182681,30.258029;120.183486,30.258021;120.184516,30.257964;120.187017,30.257882;120.187112,30.257895;120.187207,30.257871;120.187245,30.257869;120.187306,30.257916;120.187363,30.257911;120.187448,30.257908;120.187458,30.257859;120.187649,30.25785;120.188064,30.257789;120.188619,30.264008;120.188654,30.267294;120.188411,30.268937;120.188178,30.27085;120.188983,30.274327;120.188317,30.276217;120.187321,30.277816;120.187084,30.278146;120.18708,30.278161;120.187067,30.278171;120.185928,30.279761;120.184933,30.281206;120.184076,30.283091;120.184031,30.283831;120.184247,30.285377;120.184478,30.286646;120.184857,30.288093;120.184895,30.288268;120.18532,30.290201;120.185387,30.290312;120.185581,30.290349;120.18568,30.290452;120.185767,30.290724;120.185931,30.291237;120.186033,30.291803;120.186299,30.29327;120.186418,30.293498;120.186693,30.294565;120.187064,30.294784;120.187361,30.296315;120.187452,30.296786;120.188383,30.297107;120.18969,30.2974;120.190487,30.297695;120.191142,30.298237;120.19196,30.299233;120.192566,30.301006;120.192937,30.301781;120.193232,30.302102;120.193116,30.303595;120.193018,30.304746;120.188249,30.304505;120.1883,30.308764;120.189864,30.308855;120.189734,30.30944;120.189875,30.31066;120.190287,30.312331;120.190609,30.312641;120.194838,30.312087;120.195271,30.313166;120.195242,30.313434;120.19541,30.313452;120.197301,30.313052;120.197419,30.313636;120.197823,30.314271;120.197954,30.314328;120.198487,30.314002;120.198982,30.313982;120.199374,30.313655;120.200188,30.312146;120.200509,30.312676;120.200676,30.312953;120.20112,30.313415;120.201411,30.314002;120.201541,30.314042;120.202522,30.315012;120.204621,30.317195;120.206191,30.318974;120.207019,30.319785;120.207202,30.320014;120.207188,30.320121;120.206861,30.320187;120.206362,30.320289;120.205376,30.320235;120.204324,30.320486;120.204075,30.320487;120.203513,30.320368;120.201593,30.320242;120.198471,30.320301;120.196906,30.320122;120.196687,30.321793;120.196893,30.321875;120.202432,30.322034;120.20261,30.322332;120.203339,30.322929;120.203257,30.323519;120.203213,30.323772;120.202991,30.323869;120.202894,30.324178;120.203552,30.32501;120.203896,30.324928;120.204408,30.325959;120.203953,30.326089;120.203132,30.326211;120.202349,30.326238;120.202539,30.327747;120.202835,30.327827;120.202846,30.327983;120.202931,30.32917;120.203162,30.329726;120.203469,30.329707;120.203688,30.329877;120.203927,30.329912;120.204232,30.330624;120.205694,30.330608;120.207532,30.330503;120.207939,30.332297;120.208084,30.332304;120.209966,30.332165;120.211167,30.332123;120.211187,30.3333;120.211399,30.333889;120.212084,30.33415;120.212133,30.33638;120.212123,30.340261;120.212012,30.341021;120.212076,30.342088;120.21227,30.342834;120.212293,30.345334;120.209243,30.345298;120.205929,30.345567;120.205281,30.345539;120.204378,30.34574;120.204055,30.346007;120.203408,30.347153;120.202997,30.347619;120.202358,30.347675;120.20185,30.347631;120.201035,30.348129;120.200819,30.349354;120.199861,30.350989;120.201495,30.351393;120.203497,30.352209;120.204245,30.352665;120.205787,30.353973;120.206354,30.354349;120.20845,30.355851;120.210069,30.357067;120.210755,30.357583;120.211897,30.358213;120.212934,30.358711;120.213557,30.35918;120.214669,30.360936;120.215316,30.361448;120.215314,30.361564;120.214964,30.36189;120.214825,30.362165;120.215209,30.363578;120.215218,30.364031;120.214561,30.364641;120.213982,30.365461;120.213837,30.36622;120.21375,30.366379;120.213457,30.366591;120.2126,30.36681;120.212102,30.367066;120.211817,30.367386;120.211351,30.368502;120.211036,30.368841;120.210545,30.369155;120.210231,30.369487;120.210063,30.3698;120.2101,30.369863;120.210583,30.369906;120.210818,30.370021;120.211124,30.370354;120.211546,30.371038;120.211291,30.37144;120.211511,30.371853;120.211541,30.372133;120.211352,30.372472;120.21128,30.373268;120.211046,30.373492;120.210864,30.374092;120.210294,30.374833;120.209768,30.375758;120.209272,30.376836;120.209287,30.377261;120.209534,30.377675;120.209211,30.378483;120.209225,30.378963;120.209499,30.379249;120.211411,30.380373;120.21222,30.380446;120.212755,30.380634;120.212923,30.380898;120.213155,30.381687;120.213231,30.382351;120.213437,30.382751;120.214403,30.383327;120.215014,30.384033;120.215158,30.384368;120.215389,30.38461;120.215868,30.384896;120.218064,30.385588;120.218607,30.385704;120.218863,30.386011;120.219011,30.386128;120.219287,30.386345;120.220164,30.386651;120.220433,30.386834;120.222646,30.388731;120.224844,30.389114;120.225379,30.389334;120.226121,30.389938;120.226745,30.390763;120.227662,30.391696;120.228106,30.392776;120.228678,30.392326;120.231002,30.390882;120.231271,30.391243;120.231526,30.391426;120.231906,30.391501;120.232348,30.391429;120.232784,30.391569;120.233326,30.391876;120.23404,30.390901;120.234543,30.390521;120.235501,30.390184;120.236259,30.389565;120.237407,30.388373;120.237557,30.388303;120.237844,30.388361;120.238716,30.388819;120.23917,30.388385;120.240051,30.387335;120.240337,30.387231;120.24043,30.386971;120.240891,30.386597;120.241057,30.386332;120.239039,30.383801;120.236543,30.38072;120.234675,30.378113;120.234257,30.377639;120.233665,30.377164;120.232587,30.375842;120.233147,30.375268;120.233601,30.374926;120.234071,30.374368;120.234765,30.373832;120.234872,30.373809;120.23664,30.374654;120.239977,30.372152;120.241078,30.371155;120.241689,30.370738;120.241983,30.370393;120.241733,30.369846;120.241658,30.369559;120.241585,30.369283;120.241478,30.368914;120.241082,30.368149;120.240601,30.367683;120.240625,30.367473;120.241253,30.36687;120.241324,30.366922;120.242034,30.367441;120.24321,30.367116;120.24394,30.367385;120.244609,30.367428;120.244953,30.367411;120.245108,30.367199;120.24621,30.36725;120.246498,30.365515;120.246587,30.365191;120.246236,30.364947;120.246222,30.364583;120.245896,30.364252;120.245876,30.364002;120.245663,30.363731;120.245663,30.36359;120.24584,30.363079;120.24616,30.362897;120.246119,30.362715;120.246379,30.362445;120.246877,30.362127;120.247449,30.360122;120.24748,30.359663;120.247298,30.359528;120.246346,30.359269;120.243928,30.358439;120.242971,30.358136;120.242852,30.358055;120.242821,30.357903;120.243088,30.35755;120.243734,30.355673;120.243839,30.355091;120.243911,30.35357;120.244152,30.352716;120.244308,30.352347;120.244738,30.351927;120.244743,30.3514;120.244461,30.35102;120.242805,30.348936;120.241384,30.347579;120.240567,30.346988;120.238949,30.346118;120.232531,30.343023;120.231726,30.342652;120.231728,30.342454;120.232124,30.341548;120.232435,30.341324;120.232601,30.341267;120.232744,30.341216;120.232929,30.341149;120.234116,30.34041;120.235104,30.340742;120.236005,30.340941;120.238009,30.34175;120.240708,30.342869;120.240887,30.342912;120.241079,30.342632;120.241756,30.342111;120.242346,30.341631;120.242898,30.341332;120.243121,30.340833;120.243863,30.339501;120.244081,30.338959;120.244485,30.33863;120.245902,30.339011;120.249188,30.339645;120.250696,30.339936;120.251747,30.339695;120.252136,30.339155;120.253048,30.337426;120.254151,30.337897;120.254802,30.338091;120.255072,30.338406;120.255734,30.338719;120.256238,30.338817;120.256573,30.338686;120.259362,30.337999;120.259941,30.338079;120.26134,30.338519;120.26148,30.338624;120.261521,30.33885;120.261779,30.338959;120.262004,30.338737;120.262448,30.33794;120.262489,30.337587;120.260489,30.336269;120.259741,30.335888;120.258534,30.335425;120.258749,30.335171;120.258221,30.33483;120.258642,30.334116;120.259388,30.333778;120.259874,30.332975;120.260467,30.332138;120.260512,30.331795;120.260709,30.33172;120.261057,30.331805;120.261189,30.331724;120.261218,30.331588;120.261072,30.331322;120.26155,30.330115;120.262579,30.329217;120.26285,30.32907;120.263008,30.328726;120.263575,30.327612;120.263594,30.327573;120.264475,30.326382;120.265479,30.326951;120.265548,30.327032;120.265455,30.327246;120.265684,30.327423;120.266671,30.327886;120.267893,30.328579;120.267963,30.328621;120.267905,30.328856;120.266976,30.330531;120.266448,30.331321;120.271436,30.334179;120.272791,30.335048;120.273607,30.335489;120.275222,30.336493;120.277879,30.337952;120.27794,30.3379;120.27932,30.336692;120.279511,30.335894;120.279494,30.335586;120.279641,30.335047;120.279884,30.334464;120.280579,30.332819;120.280601,30.332808;120.28059,30.332786;120.280681,30.332521;120.28072,30.332525;120.280692,30.332489;120.280779,30.332235;120.281482,30.330877;120.281516,30.330804;120.28189,30.328497;120.28182,30.328488;120.281264,30.328223;120.281013,30.328049;120.280526,30.327441;120.276263,30.323298;120.274422,30.322418;120.273701,30.321913;120.273163,30.321622;120.273016,30.32147;120.272911,30.321279";
        String[] split = a.split(";");

        StringBuilder sb = new StringBuilder();
        for (String s : split) {
            sb.append("[").append(s).append("],");
        }

        String result = "[" + sb.substring(0, sb.length() - 1) + "]";
        CustomFenceAreaEsEntity customFenceAreaEsEntity = new CustomFenceAreaEsEntity();

        customFenceAreaEsEntity.setId("123");
        customFenceAreaEsEntity.setAdCodeMsgId(321);
        customFenceAreaEsEntity.setProvince("浙江");
        customFenceAreaEsEntity.setCity("杭州市");
        customFenceAreaEsEntity.setArea("上城区");
        customFenceAreaEsEntity.setGeoShape(result);
        customFenceAreaEsEntity.setStatus(0);

        customFenceAreaEsCommandRepository.saveAll(Arrays.asList(customFenceAreaEsEntity));
    }
}
